/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-07-05 01:10:00
 * @Description: 商品详情处理组合函数
 * @LastEditTime: 2025-07-15 18:59:40
 * @LastEditors: houbaoguo
 */
import { getDataCarousel, getProductDetail, getSimilarDiscount } from '@/api'

/**
 * 商品详情处理组合函数
 */
export function useProductDetail(callbackChangeToggleDesc: () => void) {
  const { toHref } = useRouter()
  // 响应式数据
  const productInfo = ref<Record<string, any>>({})
  const productValue = ref<Record<string, any>>({})
  const attrInfo = ref<Record<string, any>>({
    productAttr: [],
    productSelect: {},
  })
  const swiperList = ref<string[]>([])
  const similarDiscountList = ref<any[]>([])
  const bubbleList = ref<string[]>([])

  // 属性选择相关状态
  const attrValue = ref('')
  const attrTxt = ref('请选择')

  /**
   * 处理产品属性数据
   */
  function processProductAttr(productAttr: any[]) {
    return productAttr.map((item: any) => ({
      attrName: item.attrName,
      attrValues: item.attrValues.split(','),
      id: item.id,
      isDel: item.isDel,
      productId: item.productId,
      type: item.type,
    }))
  }

  /**
   * 处理轮播图数据
   */
  function processSliderImages(sliderImage: string) {
    try {
      return JSON.parse(sliderImage || '[]')
    }
    catch (error) {
      console.warn('解析轮播图数据失败:', error)
      return []
    }
  }

  /**
   * 更新产品选择信息的辅助函数
   */
  function updateProductSelect(
    selectData: {
      image: string
      price: number
      stock: number
      id: string | number
    },
    attrValueStr: string,
    attrTxtStr: string,
  ) {
    attrInfo.value.productSelect = {
      storeName: productInfo.value.storeName,
      image: selectData.image,
      price: selectData.price,
      stock: selectData.stock,
      unique: selectData.id,
      cart_num: 1,
    }
    attrValue.value = attrValueStr
    attrTxt.value = attrTxtStr
  }

  /**
   * 默认选中属性
   */
  function defaultSelect() {
    const productAttr = attrInfo.value.productAttr
    let value: string[] = []

    // 查找第一个有库存的商品规格
    for (const key in productValue.value) {
      if (productValue.value[key].stock > 0) {
        value = productAttr.length ? key.split(',') : []
        break
      }
    }

    // 为每个属性设置默认选中的索引
    for (let i = 0; i < productAttr.length; i++) {
      productAttr[i].index = value[i]
    }

    const productSelect = productValue.value[value.join(',')]

    // 根据不同情况设置产品选择信息
    if (productSelect && productAttr.length) {
      // 有匹配的规格且有属性
      updateProductSelect(productSelect, value.join(','), '已选择')
    }
    else if (!productSelect && productAttr.length) {
      // 无匹配规格但有属性
      updateProductSelect(
        {
          image: productInfo.value.image,
          price: productInfo.value.price,
          stock: 0,
          id: productInfo.value.id,
        },
        '',
        '请选择',
      )
    }
    else if (!productSelect && !productAttr.length) {
      // 无规格无属性
      updateProductSelect(
        {
          image: productInfo.value.image,
          price: productInfo.value.price,
          stock: productInfo.value.stock,
          id: productInfo.value.id || '',
        },
        '',
        '请选择',
      )
    }
  }

  /**
   * 获取商品详情
   */
  async function getGoodsDetails(id: string | number) {
    try {
      const res = await getProductDetail({ id }, {
        custom: {
          toast: false,
        },
      })
      const data = res?.data

      if (!data) {
        console.warn('获取商品详情失败: 数据为空')
        return
      }

      // 处理基础商品信息
      productInfo.value = data.productInfo || {}
      productInfo.value.cateIds = data.cateIds || []
      productInfo.value.commissionAmount = data.commissionAmount || '--'

      // 处理轮播图数据
      swiperList.value = processSliderImages(productInfo.value?.sliderImage)

      // 处理商品规格值
      productValue.value = data.productValue || {}

      // 处理商品属性
      const productAttr = data.productAttr || []
      attrInfo.value.productAttr = processProductAttr(productAttr)

      // 设置默认选中属性
      defaultSelect()
    }
    catch (error) {
      console.error('获取商品详情失败:', error)
      throw error
    }
  }

  /**
   * 获取相似优惠
   */
  async function getRecommendGoods(cateId: string | number, productId: string | number) {
    try {
      const res = await getSimilarDiscount({ id: cateId })
      const tempList = res?.data?.list || []
      similarDiscountList.value = tempList.filter((item: any) => item.id.toString() !== productId.toString())
    }
    catch (error) {
      console.error('获取相似优惠失败:', error)
      // 不显示错误提示，静默失败
      similarDiscountList.value = []
    }
    if (!similarDiscountList.value.length) {
      callbackChangeToggleDesc()
    }
  }

  /**
   * 获取商品详情轮播图数据
   */
  async function getBubbleList() {
    try {
      const res = await getDataCarousel()
      bubbleList.value = res?.data?.contents || []
    }
    catch (error) {
      console.error('获取气泡提示数据失败:', error)
      // 不显示错误提示，静默失败
      bubbleList.value = []
    }
  }

  // 页面加载状态
  const pageLoading = ref(true)

  /**
   * 并行获取页面所需的所有数据
   */
  async function loadPageData(productId: string | number) {
    pageLoading.value = true

    try {
      const promises = [
        getGoodsDetails(productId),
        // getBubbleList(),
      ]

      // 使用 Promise.allSettled 确保即使某个请求失败，其他请求仍能正常执行
      const results = await Promise.allSettled(promises)
      // 获取商品详情后，再获取相似优惠
      const cateIdStr = productInfo.value.cateId || ''
      const cateId = cateIdStr.split(',')[0]
      await getRecommendGoods(cateId, productId)

      // 记录失败的请求
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          if (index === 0) {
            // 商品详情请求失败，跳转到空页面
            toHref('/pages/main/product/empty', 'redirectTo')
          }
          const requestNames = ['商品详情', '气泡提示']
          console.error(`${requestNames[index]}请求失败:`, result.reason)
        }
      })
    }
    finally {
      // 添加短暂延迟确保DOM渲染完成
      setTimeout(() => {
        pageLoading.value = false
      }, 100)
    }
  }

  /**
   * 获取选中的规格值
   */
  function getCheckedValue() {
    const productAttr = attrInfo.value.productAttr
    const checkedValue = []
    for (let i = 0; i < productAttr.length; i++) {
      for (let j = 0; j < productAttr[i].attrValues.length; j++) {
        if (productAttr[i].index === productAttr[i].attrValues[j]) {
          checkedValue.push(productAttr[i].attrValues[j])
        }
      }
    }
    return checkedValue.join(',')
  }

  /**
   * 更新产品选择信息
   */
  function updateProductByChangeAttr(attr: Record<string, any>) {
    const productAttr = attrInfo.value.productAttr
    productAttr[attr.index].index = productAttr[attr.index].attrValues[attr.cIndex]
    const checkedValue = getCheckedValue()
    const productSelect = productValue.value[checkedValue]
    if (productSelect) {
      attrInfo.value.productSelect = {
        ...attrInfo.value.productSelect,
        image: productSelect.image,
        price: productSelect.price,
        stock: productSelect.stock,
        unique: productSelect.id,
        cart_num: 1,
      }
      attrValue.value = productAttr[attr.index].index
      attrTxt.value = '已选择'
    }
    else {
      attrInfo.value.productSelect = {
        ...attrInfo.value.productSelect,
        image: productInfo.value.image,
        price: productInfo.value.price,
        stock: 0,
        unique: productInfo.value.id,
        cart_num: 1,
      }
      attrValue.value = ''
      attrTxt.value = '请选择'
    }
    console.log('attrInfo.value.productSelect', attrInfo.value.productSelect)
  }

  return {
    // 响应式数据
    productInfo,
    productValue,
    attrInfo,
    swiperList,
    similarDiscountList,
    bubbleList,
    attrValue,
    attrTxt,
    pageLoading,

    // 方法
    defaultSelect,
    getGoodsDetails,
    getRecommendGoods,
    getBubbleList,
    loadPageData,
    updateProductByChangeAttr,
  }
}
