<!--
 * @Author: houbaoguo
 * @Date: 2025-07-31 15:10:59
 * @Description:
 * @LastEditTime: 2025-08-01 10:16:45
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { GoodsCategoryRes, GoodsListResItem } from '@/api/types/goods'
import { getGoodsCategory, getRecommendList } from '@/api'
import { useUserStore } from '@/store'
import goodsCardItem from './components/goods-card-item.vue'

const { toHref } = useRouter()
const userStore = useUserStore()
const pageLoading = ref(false)
const { mescrollInit } = useScroll(onPageScroll, onReachBottom)

// 是否登录
const isLogin = computed(() => {
  return !!userStore.access_token
})
const scrollConfig = computed(() => ({
  auto: false,
  use: true,
  offset: 50,
  textLoading: '加载中...',
  textNoMore: '到底了～',
}))

// 计算可滚动高度
const scrollHeight = computed(() => {
  const { windowHeight, statusBarHeight = 0 } = uni.getSystemInfoSync()
  return `${windowHeight - statusBarHeight - 44}px`
})

// 跳转商品详情
function handleGoToDetail(item: any) {
  toHref(`/pages/main/product/details?id=${item.id}&isLogin=${isLogin.value}`)
}

// 推荐列表
const recommendList = ref<GoodsListResItem[]>([])
async function getRecommendListHandler() {
  const res = await getRecommendList({
    id: 1,
    page: 1,
    limit: 3,
    type: 1,
  })
  recommendList.value = res.data.list
}

// 商品分类
const activeTab = ref()
const goodsCategory = ref<GoodsCategoryRes[]>([])
async function getGoodsCategoryHandler() {
  const res = await getGoodsCategory()
  goodsCategory.value = res.data || []
}

// 立即购买
// async function handleBuy(item: any) {
//   toHref(`/pages/main/product/details?id=${item.id}&isLogin=${isLogin.value}`)
// }

function handleScrollToLower() {
  console.log('到底了～')
}

onMounted(() => {
  getRecommendListHandler()
  getGoodsCategoryHandler()
})
</script>

<template>
  <div class="product-home">
    <image
      v-if="!pageLoading"
      src="@img/product/page-bg.png"
      class="product-details__bg"
    />
    <fx-navbar background="transparent">
      <template #nav-content>
        <div class="nav-content">
          <image
            src="@img/product/logo.png"
            class="logo-img"
          />
        </div>
      </template>
    </fx-navbar>
    <fx-scroll
      class="product-home__scroll"
      :height="scrollHeight"
      :up="scrollConfig"
      :down="{
        use: false,
      }"
      @init="mescrollInit"
      @up="handleScrollToLower"
    >
      <view class="product-home__content">
        <!-- 搜索 && 我的订单 -->
        <view class="action-box">
          <view class="search-box">
            <view class="search-box__input">
              <image
                src="@img/product/search-icon.png"
                class="search-box__icon"
              />
              <text class="placeholder-text">
                输入商品名称查找
              </text>
              <div class="search-box__button">
                搜索
              </div>
            </view>
          </view>
          <view class="own-order-btn">
            <fx-button
              :custom-style="{
                '--fx-primary-color': '#fff',
                'color': '#151D32',
                'fontSize': '26rpx',
                'borderRadius': '244rpx',
                'padding': '0 24rpx',
                'height': '72rpx',
              }"
            >
              <image
                src="@img/product/own-order-icon.png"
                class="own-order-btn__icon"
              />
              <text>我的订单</text>
            </fx-button>
          </view>
        </view>
        <!-- 精选推荐 -->
        <view class="recommend-box">
          <view class="recommend-box__title">
            <image
              src="@img/product/recommend-title.png"
              class="recommend-box__title-img"
            />
            <view class="recommend-box__more">
              <text>更多</text>
              <image
                src="@img/product/arrow-right-more.png"
                class="recommend-box__more-img"
              />
            </view>
          </view>
          <view class="recommend-box__list">
            <view
              v-for="item in recommendList"
              :key="item.id"
              class="recommend-box__list-item"
            >
              <goods-card-item
                :item="item"
                @card-tap="handleGoToDetail(item)"
              />
            </view>
          </view>
        </view>
        <!-- 商品分类 -->
        <view class="goods-category-box">
          <fx-tabs
            v-model="activeTab"
            :title-scroll="true"
            :animated-time="0"
            bar-width="40%"
          >
            <fx-tab-item
              v-for="item in goodsCategory"
              :key="item.id"
              :name="item.id"
              :title="item.name"
            >
              <view class="goods-category-box__item">
                <text>{{ item.name }}</text>
              </view>
            </fx-tab-item>
          </fx-tabs>
        </view>
      </view>
    </fx-scroll>
  </div>
</template>

<style scoped lang="scss">
.product-home {
  position: relative;
  min-height: 100vh;
  background: #f5f5f5;
  box-sizing: border-box;
  .product-details__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 540rpx;
    z-index: 0;
  }
  .nav-content {
    height: 100%;
    padding: 0 32rpx;
    display: flex;
    align-items: center;
    .logo-img {
      width: 160rpx;
      height: 40rpx;
    }
  }
  &__content {
    position: relative;
    z-index: 1;
    .action-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16rpx 24rpx;
      .search-box {
        flex: 1;
        margin-right: 16rpx;
        .search-box__input {
          position: relative;
          width: 100%;
          height: 72rpx;
          background: #fff;
          border-radius: 60rpx;
          display: flex;
          align-items: center;
          padding: 16rpx 24rpx;
          box-sizing: border-box;
          .search-box__icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 12rpx;
          }
          .placeholder-text {
            font-size: 28rpx;
            color: #7b7b7b;
            line-height: 40rpx;
            font-weight: 400;
          }
          .search-box__button {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 8rpx;
            right: 8rpx;
            width: 102rpx;
            height: 56rpx;
            background: #ff7040;
            border-radius: 60rpx;
            font-size: 26rpx;
            color: #fff;
            line-height: 36rpx;
            font-weight: 500;
          }
        }
      }
      .own-order-btn {
        display: flex;
        align-items: center;
        .own-order-btn__icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }
      }
    }
    .recommend-box {
      margin: 24rpx 24rpx 0;
      padding: 20rpx 24rpx 24rpx;
      background-image: url('@img/product/recommend-bg.png');
      background-size: 330rpx 194rpx;
      background-repeat: no-repeat;
      background-position: 0 0;
      height: 490rpx;
      border-radius: 24rpx;
      border: 2rpx solid #ffffff;
      box-sizing: border-box;
      background-color: #fff;
      .recommend-box__title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .recommend-box__title-img {
          width: 129.86rpx;
          height: 51.28rpx;
        }
        .recommend-box__more {
          display: flex;
          align-items: center;
          font-size: 24rpx;
          color: #787c89;
          line-height: 34rpx;
          font-weight: 400;
          .recommend-box__more-img {
            width: 24rpx;
            height: 24rpx;
          }
        }
      }
      .recommend-box__list {
        margin-top: 20rpx;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .recommend-box__list-item {
          width: calc((100% - 32rpx) / 3);
          margin-bottom: 20rpx;
          box-sizing: border-box;
        }
      }
    }
  }
}
</style>
