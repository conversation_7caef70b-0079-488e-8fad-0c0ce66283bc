<!--
 * @Author: houbaoguo
 * @Date: 2025-02-24 13:53:55
 * @Description:
 * @LastEditTime: 2025-08-01 10:21:26
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { ComponentInternalInstance, CSSProperties } from 'vue'
import { debounce, getRandomId } from '@/utils'
import raf from '@/utils/raf'
import { TAB_KEY } from './tabs'

interface TabItem {
  name: string
  title: string
  disabled: boolean
}

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
  },
  activeColor: {
    type: String,
    default: '#030E2E',
  },
  barColor: {
    type: String,
    default: 'linear-gradient( 270deg, #479AFF 0%, #0256FF 100%);',
  },
  barWidth: {
    type: String,
    default: '100%',
  },
  animatedTime: {
    type: Number,
    default: 300,
  },
  titleScroll: {
    type: Boolean,
    default: false,
  },
  titleHeight: {
    type: Number,
    default: 88,
  },
})
const emit = defineEmits(['update:modelValue', 'change'])
const instance = getCurrentInstance() as ComponentInternalInstance
const { getSelectorNodeInfo, getSelectorNodeInfos } = useSelectorQuery(instance)
const refRandomId = getRandomId()

const { internalChildren } = useProvide(TAB_KEY, 'fx-tabs')({
  activeIndex: computed(() => props.modelValue || ''),
  animatedTime: computed(() => props.animatedTime),
})
// 当前激活索引
const activeIndex = ref(0)
const scrollWithAnimation = ref(false)
const navRectRef = ref()
const titleRectRef = ref<UniApp.NodeInfo[]>([])
const scrollLeft = ref(0)
const canShowLabel = ref(false)
const innerTabs = ref<TabItem[]>([])

// 滑动条相关状态
const barLeft = ref(0)
const barWidth = ref(0)
const barTransition = ref(false)

const tabsContentStyle = computed<CSSProperties>(() => {
  const offsetPercent = activeIndex.value * 100
  console.log('offsetPercent', offsetPercent)
  return {
    transform: `translate3d(-${offsetPercent}%, 0, 0)`,
    transitionDuration: `${props.animatedTime}ms`,
  }
})

// 滑动条样式
const barStyle = computed<CSSProperties>(() => {
  return {
    position: 'absolute',
    bottom: '0',
    left: `${barLeft.value}px`,
    width: `${barWidth.value}px`,
    height: '6rpx',
    borderRadius: '8rpx',
    background: props.barColor,
    transform: 'translateZ(0)',
    transition: barTransition.value ? `all 300ms ease-in-out` : 'none',
    zIndex: 0,
  }
})

// 当通过插槽传入tabs时，提取子组件属性name和title
function extractTabs(vnodes: VNode[]) {
  vnodes?.forEach((vnode: VNode) => {
    let type = vnode.type
    type = (type as any)?.name || type
    if (type === 'FxTabItem') {
      const item: TabItem = {
        name: '',
        title: '',
        disabled: false,
      }
      if (vnode.props?.title || vnode.props?.name || vnode.props?.disabled) {
        item.name = vnode.props?.name || ''
        item.title = vnode.props?.title || ''
        item.disabled = vnode.props?.disabled || false
      }
      innerTabs.value.push(item)
    }
    else {
      if (vnode.children === ' ')
        return

      extractTabs(vnode.children as VNode[])
    }
  })
  console.log('innerTabs', innerTabs.value)
}

// 更新滑动条位置
function updateBarPosition() {
  raf(() => {
    Promise.all([
      getSelectorNodeInfo(`#fx-tabs__headers_${refRandomId}`),
      getSelectorNodeInfos(`#fx-tabs__headers_${refRandomId} .fx-tabs-header-item`),
    ]).then(([navRect, titleRects]) => {
      navRectRef.value = navRect
      titleRectRef.value = titleRects

      if (titleRects && titleRects[activeIndex.value]) {
        const currentRect = titleRects[activeIndex.value]
        const leftOffset = titleRects
          .slice(0, activeIndex.value)
          .reduce((prev: number, curr) => prev + curr.width!, 0)

        // 计算滑动条的位置和宽度
        const itemWidth = currentRect.width!
        let calculatedBarWidth = itemWidth

        // 如果设置了 barWidth 属性，按比例计算
        if (props.barWidth !== '100%') {
          if (props.barWidth.endsWith('%')) {
            const percentage = Number.parseFloat(props.barWidth) / 100
            calculatedBarWidth = itemWidth * percentage
          }
          else {
            calculatedBarWidth = Number.parseFloat(props.barWidth)
          }
        }

        // 计算滑动条的左边距（居中对齐）
        const barLeftOffset = leftOffset + (itemWidth - calculatedBarWidth) / 2

        // 启用过渡动画
        barTransition.value = true
        barLeft.value = barLeftOffset
        barWidth.value = calculatedBarWidth
      }
    })
  })
}

function scrollIntoView() {
  if (!props.titleScroll)
    return
  raf(() => {
    Promise.all([
      getSelectorNodeInfo(`#fx-tabs__headers_${refRandomId}`),
      getSelectorNodeInfos(`#fx-tabs__headers_${refRandomId} .fx-tabs-header-item`),
    ]).then(([navRect, titleRects]) => {
      console.log('navRect', navRect)
      console.log('titleRects', titleRects)
      navRectRef.value = navRect
      titleRectRef.value = titleRects

      if (navRectRef.value) {
        const titlesTotalWidth = titleRects.reduce((prev: number, curr: UniApp.NodeInfo) => prev + curr.width!, 0)
        if (titlesTotalWidth > navRectRef.value?.width)
          canShowLabel.value = true

        else
          canShowLabel.value = false
      }

      const titleRect: UniApp.NodeInfo = titleRectRef.value[activeIndex.value]

      let to = 0
      const left = titleRects
        .slice(0, activeIndex.value)
        .reduce((prev: number, curr) => prev + curr.width!, 0)
        // eslint-disable-next-line  ts/no-non-null-asserted-optional-chain
      to = left - (navRectRef.value?.width - titleRect?.width!) / 2

      nextTick(() => {
        scrollWithAnimation.value = true
      })

      scrollDirection(to)
    })
  })
}

function scrollDirection(to: number) {
  let count = 0
  const from = scrollLeft.value
  const frames = 1

  function animate() {
    scrollLeft.value += (to - from) / frames

    if (++count < frames)
      raf(animate)
    console.log('scrollLeft', scrollLeft.value)
  }

  animate()
}

function initTabs(vnodes: VNode[] = internalChildren.map(item => item.vnode)) {
  innerTabs.value = []
  vnodes = vnodes?.filter(item => typeof item.children !== 'string')

  if (vnodes && vnodes.length)
    extractTabs(vnodes)

  setTimeout(() => {
    scrollIntoView()
    updateBarPosition()
  }, 500)
}

// 处理tab点击
function handleTabClick(index: number, item: TabItem) {
  if (activeIndex.value === index) return
  activeIndex.value = index

  emit('update:modelValue', item.name)
  emit('change', item)

  // 更新滑动条位置
  nextTick(() => {
    updateBarPosition()
  })
}

// 找到tabs的index
function findTabsIndex(value: string | number) {
  const index = innerTabs.value.findIndex(item => item.name === value)
  if (index !== -1)
    activeIndex.value = index
}

// 监听内部子组件的props
watch(
  () => internalChildren.map(item => item.props),
  () => {
    // 防抖处理
    debounce(initTabs, 100)(internalChildren as any[])
    findTabsIndex(props.modelValue)
  },
  { deep: true, immediate: true },
)

// 监听modelValue
watch(
  () => props.modelValue,
  (value: string | number) => {
    findTabsIndex(value)
    scrollIntoView()
    nextTick(() => {
      updateBarPosition()
    })
  },
)

onMounted(initTabs)
onActivated(initTabs)
</script>

<template>
  <view class="fx-tabs">
    <scroll-view
      :id="`fx-tabs__headers_${refRandomId}`"
      :scroll-x="titleScroll"
      :scroll-with-animation="scrollWithAnimation"
      :scroll-left="scrollLeft"
      :enable-flex="true"
      class="fx-tabs__headers"
      :class="{ scrollable: titleScroll }"
      :style="{ height: `${props.titleHeight}rpx` }"
    >
      <view class="fx-tabs__list">
        <view
          v-for="(item, index) in innerTabs"
          :key="item.name"
          class="fx-tabs-header-item"
          :class="{ 'is-active': activeIndex === index }"
          @click="handleTabClick(index, item)"
        >
          <text class="fx-tabs-header-item-text">
            {{ item.title }}
          </text>
        </view>
        <!-- 滑动条 -->
        <view class="fx-tabs-bar" :style="barStyle" />
      </view>
    </scroll-view>
    <view class="fx-tabs-content" :style="tabsContentStyle">
      <slot />
    </view>
  </view>
</template>

<style scoped lang="scss">
.fx-tabs {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.fx-tabs__headers {
  display: flex;
  flex-shrink: 0;
  white-space: nowrap;
  user-select: none;

  .fx-tabs__list {
    display: flex;
    flex-shrink: 0;
    width: 100%;
    position: relative;
  }
}

.fx-tabs-header-item {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #787c89;
  transition: color 0.3s;
  position: relative;
  z-index: 1; // 确保在滑动条上方
  white-space: nowrap;
  user-select: none;
  height: 100%;
  &-text {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20rpx;
  }

  &.is-active {
    color: v-bind('props.activeColor');
    font-weight: 500;
  }
}

.fx-tabs-bar {
  position: absolute;
  bottom: 0;
  height: 6rpx;
  border-radius: 8rpx;
  z-index: 0;
}

.fx-tabs-content {
  flex: 1;
  display: flex;
  box-sizing: border-box;
  max-height: calc(100% - 88rpx);
  // overflow: hidden;
}
</style>
